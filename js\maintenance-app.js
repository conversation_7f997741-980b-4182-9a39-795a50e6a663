// صيانة بلدي - JavaScript للتطبيق

// متغيرات عامة
let map;
let userLocation = null;
let reports = [];
let currentFilter = 'all';

// تهيئة التطبيق عند تحميل الصفحة
$(document).ready(function() {
    initializeApp();
    initializeMap();
    loadSampleReports();
    bindEvents();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('تم تهيئة تطبيق صيانة بلدي');
    
    // تحديث الإحصائيات
    updateStatistics();
    
    // تحقق من دعم الموقع الجغرافي
    if (!navigator.geolocation) {
        alert('متصفحك لا يدعم تحديد الموقع الجغرافي');
    }
}

// تهيئة الخريطة التفاعلية
function initializeMap() {
    // إحداثيات بغداد كمركز افتراضي
    const defaultLat = 33.3152;
    const defaultLng = 44.3661;

    // إنشاء الخريطة
    map = L.map('interactive-map').setView([defaultLat, defaultLng], 10);
    
    // إضافة طبقة الخريطة
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // إضافة أيقونات مخصصة للأنواع المختلفة
    createCustomIcons();
}

// إنشاء أيقونات مخصصة
function createCustomIcons() {
    window.reportIcons = {
        pothole: L.divIcon({
            className: 'custom-marker pothole-marker',
            html: '<i class="fa fa-road" style="color: #e74c3c; font-size: 20px;"></i>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        }),
        bump: L.divIcon({
            className: 'custom-marker bump-marker',
            html: '<i class="fa fa-warning" style="color: #f39c12; font-size: 20px;"></i>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        }),
        water: L.divIcon({
            className: 'custom-marker water-marker',
            html: '<i class="fa fa-tint" style="color: #3498db; font-size: 20px;"></i>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        }),
        sidewalk: L.divIcon({
            className: 'custom-marker sidewalk-marker',
            html: '<i class="fa fa-building" style="color: #9b59b6; font-size: 20px;"></i>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        }),
        lighting: L.divIcon({
            className: 'custom-marker lighting-marker',
            html: '<i class="fa fa-lightbulb-o" style="color: #f1c40f; font-size: 20px;"></i>',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
        })
    };
}

// ربط الأحداث
function bindEvents() {
    // زر تحديد الموقع
    $('#get-location').click(function() {
        getCurrentLocation();
    });
    
    // إرسال البلاغ
    $('#report-form').submit(function(e) {
        e.preventDefault();
        submitReport();
    });
    
    // فلترة البلاغات
    $('.filter-btn').click(function() {
        const filter = $(this).data('filter');
        filterReports(filter);
        
        $('.filter-btn').removeClass('active');
        $(this).addClass('active');
    });
    
    // نموذج الاتصال
    $('#contact-submit').click(function(e) {
        e.preventDefault();
        submitContactForm();
    });
}

// الحصول على الموقع الحالي
function getCurrentLocation() {
    const button = $('#get-location');
    button.text('جاري تحديد الموقع...');
    button.prop('disabled', true);
    
    navigator.geolocation.getCurrentPosition(
        function(position) {
            userLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
            };
            
            $('#latitude').val(userLocation.lat);
            $('#longitude').val(userLocation.lng);
            
            button.text('تم تحديد الموقع ✓');
            button.css('background', '#27ae60');
            
            // إضافة علامة على الخريطة
            if (window.userMarker) {
                map.removeLayer(window.userMarker);
            }
            
            window.userMarker = L.marker([userLocation.lat, userLocation.lng])
                .addTo(map)
                .bindPopup('موقعك الحالي')
                .openPopup();
            
            map.setView([userLocation.lat, userLocation.lng], 15);
        },
        function(error) {
            button.text('فشل في تحديد الموقع');
            button.css('background', '#e74c3c');
            button.prop('disabled', false);
            
            setTimeout(() => {
                button.text('تحديد الموقع');
                button.css('background', '#27ae60');
            }, 3000);
            
            console.error('خطأ في تحديد الموقع:', error);
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        }
    );
}

// إرسال البلاغ
function submitReport() {
    const formData = {
        type: $('#problem_type').val(),
        severity: $('#severity').val(),
        description: $('#description').val(),
        latitude: $('#latitude').val(),
        longitude: $('#longitude').val(),
        photo: $('#photo')[0].files[0],
        timestamp: new Date().toISOString()
    };
    
    // التحقق من البيانات المطلوبة
    if (!formData.type || !formData.severity) {
        alert('يرجى تحديد نوع المشكلة ودرجة الخطورة');
        return;
    }
    
    if (!formData.latitude || !formData.longitude) {
        alert('يرجى تحديد الموقع أولاً');
        return;
    }
    
    // إضافة البلاغ للقائمة
    const reportId = 'report_' + Date.now();
    const newReport = {
        id: reportId,
        ...formData,
        status: 'pending'
    };
    
    reports.push(newReport);
    
    // إضافة علامة على الخريطة
    addReportToMap(newReport);
    
    // تحديث الإحصائيات
    updateStatistics();
    
    // حفظ في التخزين المحلي
    saveToLocalStorage();
    
    // إظهار رسالة نجاح
    showSuccessMessage();
    
    // إعادة تعيين النموذج
    resetForm();
}

// إضافة البلاغ للخريطة
function addReportToMap(report) {
    const icon = window.reportIcons[report.type] || window.reportIcons.pothole;
    
    const marker = L.marker([report.latitude, report.longitude], { icon: icon })
        .addTo(map);
    
    const popupContent = `
        <div class="report-popup">
            <h5>${getTypeLabel(report.type)}</h5>
            <p><strong>الخطورة:</strong> ${getSeverityLabel(report.severity)}</p>
            <p><strong>الوصف:</strong> ${report.description || 'لا يوجد وصف'}</p>
            <p><strong>التاريخ:</strong> ${formatDate(report.timestamp)}</p>
            <p><strong>الحالة:</strong> ${getStatusLabel(report.status)}</p>
        </div>
    `;
    
    marker.bindPopup(popupContent);
    
    // حفظ مرجع للعلامة
    report.marker = marker;
}

// تحديث الإحصائيات
function updateStatistics() {
    const stats = {
        pothole: reports.filter(r => r.type === 'pothole').length,
        bump: reports.filter(r => r.type === 'bump').length,
        water: reports.filter(r => r.type === 'water').length,
        sidewalk: reports.filter(r => r.type === 'sidewalk').length,
        lighting: reports.filter(r => r.type === 'lighting').length
    };
    
    $('#pothole-count').text(stats.pothole + ' بلاغ');
    $('#bump-count').text(stats.bump + ' بلاغ');
    $('#water-count').text(stats.water + ' بلاغ');
    $('#sidewalk-count').text(stats.sidewalk + ' بلاغ');
    $('#lighting-count').text(stats.lighting + ' بلاغ');
}

// فلترة البلاغات
function filterReports(filter) {
    currentFilter = filter;
    
    reports.forEach(report => {
        if (report.marker) {
            if (filter === 'all' || report.type === filter) {
                map.addLayer(report.marker);
            } else {
                map.removeLayer(report.marker);
            }
        }
    });
}

// تحميل بيانات تجريبية
function loadSampleReports() {
    const sampleReports = [
        {
            id: 'sample_1',
            type: 'pothole',
            severity: 'high',
            description: 'حفرة كبيرة في شارع الرشيد',
            latitude: 33.3152,
            longitude: 44.3661,
            timestamp: new Date(Date.now() - 86400000).toISOString(),
            status: 'pending'
        },
        {
            id: 'sample_2',
            type: 'water',
            severity: 'medium',
            description: 'تجمع مياه في منطقة الكرادة',
            latitude: 33.3067,
            longitude: 44.3975,
            timestamp: new Date(Date.now() - 172800000).toISOString(),
            status: 'in_progress'
        },
        {
            id: 'sample_3',
            type: 'lighting',
            severity: 'medium',
            description: 'إضاءة معطلة في شارع المتنبي',
            latitude: 33.3406,
            longitude: 44.4009,
            timestamp: new Date(Date.now() - 259200000).toISOString(),
            status: 'pending'
        },
        {
            id: 'sample_4',
            type: 'sidewalk',
            severity: 'high',
            description: 'انهيار رصيف في منطقة الجادرية',
            latitude: 33.2778,
            longitude: 44.3661,
            timestamp: new Date(Date.now() - 345600000).toISOString(),
            status: 'in_progress'
        },
        {
            id: 'sample_5',
            type: 'bump',
            severity: 'critical',
            description: 'مطب خطير على طريق المطار',
            latitude: 33.2628,
            longitude: 44.2350,
            timestamp: new Date(Date.now() - 432000000).toISOString(),
            status: 'pending'
        },
        {
            id: 'sample_6',
            type: 'drainage',
            severity: 'high',
            description: 'انسداد في شبكة الصرف - البصرة',
            latitude: 30.5085,
            longitude: 47.7804,
            timestamp: new Date(Date.now() - 518400000).toISOString(),
            status: 'in_progress'
        },
        {
            id: 'sample_7',
            type: 'pothole',
            severity: 'medium',
            description: 'حفرة في شارع الجامعة - أربيل',
            latitude: 36.1911,
            longitude: 44.0093,
            timestamp: new Date(Date.now() - 604800000).toISOString(),
            status: 'completed'
        },
        {
            id: 'sample_8',
            type: 'lighting',
            severity: 'low',
            description: 'إضاءة ضعيفة في حي الأندلس - الموصل',
            latitude: 36.3350,
            longitude: 43.1189,
            timestamp: new Date(Date.now() - 691200000).toISOString(),
            status: 'pending'
        }
    ];
    
    sampleReports.forEach(report => {
        reports.push(report);
        addReportToMap(report);
    });
    
    updateStatistics();
}

// وظائف مساعدة
function getTypeLabel(type) {
    const labels = {
        pothole: 'حفرة في الطريق',
        bump: 'مطب خطير',
        water: 'تجمع مياه',
        sidewalk: 'انهيار رصيف',
        lighting: 'إضاءة معطلة',
        drainage: 'مشكلة صرف',
        other: 'أخرى'
    };
    return labels[type] || type;
}

function getSeverityLabel(severity) {
    const labels = {
        low: 'منخفضة',
        medium: 'متوسطة',
        high: 'عالية',
        critical: 'حرجة'
    };
    return labels[severity] || severity;
}

function getStatusLabel(status) {
    const labels = {
        pending: 'في الانتظار',
        in_progress: 'قيد المعالجة',
        completed: 'مكتمل',
        rejected: 'مرفوض'
    };
    return labels[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function showSuccessMessage() {
    alert('تم إرسال البلاغ بنجاح! شكراً لمساهمتك في تحسين المدينة.');
}

function resetForm() {
    $('#report-form')[0].reset();
    $('#get-location').text('تحديد الموقع').css('background', '#27ae60').prop('disabled', false);
    $('#latitude').val('');
    $('#longitude').val('');
    userLocation = null;
    
    if (window.userMarker) {
        map.removeLayer(window.userMarker);
    }
}

function saveToLocalStorage() {
    localStorage.setItem('maintenance_reports', JSON.stringify(reports));
}

function loadFromLocalStorage() {
    const saved = localStorage.getItem('maintenance_reports');
    if (saved) {
        reports = JSON.parse(saved);
        reports.forEach(report => addReportToMap(report));
        updateStatistics();
    }
}

function submitContactForm() {
    const name = $('#contact_name').val();
    const subject = $('#contact_subject').val();
    const message = $('#contact_message').val();
    
    if (!name || !subject || !message) {
        alert('يرجى ملء جميع الحقول');
        return;
    }
    
    // هنا يمكن إضافة كود إرسال الرسالة للخادم
    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
    
    // إغلاق النافذة المنبثقة
    $('#modal').hide();
    
    // إعادة تعيين النموذج
    $('#contact_name, #contact_subject, #contact_message').val('');
}
