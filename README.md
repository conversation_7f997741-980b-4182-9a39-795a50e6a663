# صيانة بلدي - تطبيق الإبلاغ عن مشاكل الشوارع

## نظرة عامة

تطبيق "صيانة بلدي" هو مبادرة مجتمعية تهدف إلى تحسين جودة الطرق والشوارع من خلال تمكين المواطنين من الإبلاغ السريع والمباشر عن المشاكل دون الحاجة لتسجيل الدخول أو مشاركة بيانات شخصية.

## المميزات الرئيسية

### 🚀 إبلاغ سريع ومباشر
- إبلاغ فوري عن مشاكل الشوارع بدون تسجيل دخول
- دعم أنواع مختلفة من المشاكل (حفر، مطبات، تجمع مياه، انهيار أرصفة، إضاءة معطلة)
- تحديد درجة الخطورة (منخفضة، متوسطة، عالية، حرجة)

### 🗺️ خريطة تفاعلية
- عرض جميع البلاغات على خريطة تفاعلية
- فلترة البلاغات حسب النوع والحالة
- تحديد الموقع بدقة باستخدام GPS
- أيقونات مميزة لكل نوع من المشاكل

### 📊 إحصائيات شاملة
- عرض إحصائيات البلاغات في الوقت الفعلي
- تصنيف المشاكل حسب النوع والشدة
- مؤشرات بصرية لحالة البلاغات

### 📱 تطبيق ويب تقدمي (PWA)
- يعمل على الهواتف الذكية والحاسوب
- قابل للتثبيت كتطبيق محلي
- يعمل بدون إنترنت مع حفظ البلاغات محلياً
- مزامنة تلقائية عند توفر الاتصال

### 🔒 حماية الخصوصية
- لا يتطلب تسجيل دخول أو بيانات شخصية
- إبلاغ مجهول تماماً
- حفظ البيانات محلياً فقط

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 3.x
- **Maps**: Leaflet.js
- **Fonts**: Cairo (للعربية), Raleway (للإنجليزية)
- **Icons**: Font Awesome
- **PWA**: Service Worker, Web App Manifest
- **Storage**: LocalStorage, IndexedDB

## هيكل المشروع

```
├── index.html              # الصفحة الرئيسية
├── manifest.json           # ملف PWA Manifest
├── sw.js                   # Service Worker للعمل بدون إنترنت
├── css/
│   ├── bootstrap.min.css   # إطار عمل Bootstrap
│   ├── fontAwesome.css     # أيقونات Font Awesome
│   ├── templatemo-style.css # التصميم الأساسي
│   └── maintenance-style.css # تصميم مخصص للتطبيق
├── js/
│   ├── vendor/             # مكتبات خارجية
│   ├── main.js            # JavaScript الأساسي
│   └── maintenance-app.js  # وظائف التطبيق الرئيسية
├── fonts/                  # ملفات الخطوط
├── img/                    # الصور والأيقونات
└── README.md              # هذا الملف
```

## كيفية الاستخدام

### للمواطنين:

1. **الإبلاغ عن مشكلة**:
   - اختر نوع المشكلة من القائمة المنسدلة
   - حدد درجة الخطورة
   - اضغط على "تحديد الموقع" للحصول على إحداثيات GPS
   - أضف وصف للمشكلة (اختياري)
   - ارفع صورة للمشكلة (اختياري)
   - اضغط "إرسال البلاغ"

2. **عرض البلاغات**:
   - انتقل لقسم "خريطة البلاغات"
   - استخدم أزرار الفلترة لعرض نوع معين من المشاكل
   - انقر على أي علامة في الخريطة لعرض تفاصيل البلاغ

3. **متابعة الإحصائيات**:
   - راجع قسم "الإحصائيات" لمعرفة حالة البلاغات في المدينة

### للجهات المختصة:

- عرض جميع البلاغات على الخريطة التفاعلية
- فلترة البلاغات حسب النوع والأولوية
- تحديد المواقع بدقة للوصول السريع
- متابعة الإحصائيات لتحديد الأولويات

## التثبيت والتشغيل

### متطلبات النظام:
- خادم ويب (Apache, Nginx, أو أي خادم آخر)
- متصفح حديث يدعم HTML5 و Service Workers

### خطوات التثبيت:

1. **نسخ الملفات**:
   ```bash
   git clone [repository-url]
   cd maintenance-city
   ```

2. **تشغيل الخادم**:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # أو باستخدام Node.js
   npx http-server
   
   # أو باستخدام PHP
   php -S localhost:8000
   ```

3. **فتح التطبيق**:
   - افتح المتصفح وانتقل إلى `http://localhost:8000`

### التثبيت كتطبيق PWA:

1. افتح التطبيق في المتصفح
2. ابحث عن خيار "إضافة إلى الشاشة الرئيسية" أو "تثبيت التطبيق"
3. اتبع التعليمات لتثبيت التطبيق محلياً

## المميزات المتقدمة

### العمل بدون إنترنت:
- حفظ البلاغات محلياً عند انقطاع الإنترنت
- مزامنة تلقائية عند عودة الاتصال
- إشعارات للمستخدم عن حالة المزامنة

### الأمان والخصوصية:
- عدم تخزين بيانات شخصية
- تشفير البيانات المحلية
- حماية من هجمات XSS و CSRF

### التجاوب مع الأجهزة:
- تصميم متجاوب يعمل على جميع الأحجام
- تحسين خاص للهواتف الذكية
- دعم اللمس والإيماءات

## المساهمة في التطوير

نرحب بمساهماتكم في تطوير التطبيق:

1. Fork المشروع
2. إنشاء branch جديد للميزة المطلوبة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح تحت رخصة MIT.

## التواصل والدعم

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 12 345 6789
- **الموقع**: www.maintenance-city.sa

## الإصدارات المستقبلية

- [ ] إضافة نظام إشعارات للجهات المختصة
- [ ] تطبيق محمول أصلي (iOS/Android)
- [ ] لوحة تحكم للإدارة
- [ ] تقارير تحليلية متقدمة
- [ ] دعم لغات إضافية
- [ ] تكامل مع أنظمة البلدية الحالية

---

**صيانة بلدي** - مبادرة مجتمعية لمدينة أفضل 🏙️
