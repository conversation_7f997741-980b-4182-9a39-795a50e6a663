/* صيانة بلدي - تطبيق الإبلاغ عن مشاكل الشوارع */

/* إعدادات عامة للغة العربية */
body {
    font-family: 'Cairo', 'Raleway', Arial, sans-serif;
    direction: rtl;
    text-align: right;
}

/* تخصيص الألوان للتطبيق البلدي */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
}

/* تحسين نموذج الإبلاغ */
.submit-form {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.submit-form h3 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* تصميم أزرار مخصصة */
.btn-location {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-location:hover {
    background: #229954;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تصميم حقل رفع الصور */
input[type="file"] {
    display: none;
}

.file-label {
    display: block;
    background: var(--info-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.file-label:hover {
    background: #138496;
    transform: translateY(-2px);
}

/* تصميم قسم الإحصائيات */
.statistics-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
}

.stats-item .thumb {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.stats-item .thumb:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.stats-icon {
    margin-bottom: 20px;
}

.status-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.status-indicator.critical {
    background: var(--danger-color);
}

.status-indicator.high {
    background: var(--warning-color);
}

.status-indicator.medium {
    background: var(--info-color);
}

.status-indicator.low {
    background: var(--success-color);
}

/* تصميم قسم الخريطة */
.map-section {
    background: var(--light-color);
    padding: 80px 0;
}

.map-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.map-filters {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

.filter-btn {
    background: var(--light-color);
    color: var(--dark-color);
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* تحسين قسم عن التطبيق */
.about-app {
    background: white;
    padding: 80px 0;
}

.service-item {
    text-align: center;
    padding: 30px 20px;
    border-radius: 15px;
    transition: all 0.3s ease;
    height: 100%;
}

.service-item:hover {
    background: var(--light-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.service-item .icon {
    margin-bottom: 25px;
}

.service-item h4 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
}

/* تحسين التجاوب مع الأجهزة المحمولة */
@media (max-width: 768px) {
    .submit-form {
        padding: 20px;
        margin: 20px 10px;
    }
    
    .filter-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .filter-btn {
        width: 200px;
    }
    
    .stats-item .thumb {
        margin-bottom: 20px;
    }
    
    .service-item {
        margin-bottom: 30px;
    }
}

/* تحسين الخطوط العربية */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

p, span, label, input, select, textarea, button {
    font-family: 'Cairo', sans-serif;
}

/* تحسين الأزرار الرئيسية */
.blue-button a {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

/* تحسين القوائم */
.dropdown.menu li a {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

/* تحسين النماذج */
.form-control {
    font-family: 'Cairo', sans-serif;
    border-radius: 8px;
    border: 2px solid #ddd;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* تحسين الفوتر */
footer {
    background: var(--primary-color);
}

footer h3, footer h4 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

footer p, footer li {
    font-family: 'Cairo', sans-serif;
}

/* تحسين الهيدر */
header .logo h3 {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
}

/* إضافة تأثيرات بصرية */
.section-heading span {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

.section-heading h2 {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
}

/* تحسين النافذة المنبثقة */
.modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.modal-btn {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

/* تحسين الأيقونات الاجتماعية */
.social-icons a {
    transition: all 0.3s ease;
}

.social-icons a:hover {
    transform: translateY(-3px);
}
