// Service Worker لتطبيق صيانة بلدي
// يوفر إمكانية العمل بدون إنترنت وحفظ البلاغات محلياً

const CACHE_NAME = 'maintenance-city-v1';
const urlsToCache = [
    '/',
    '/index.html',
    '/css/bootstrap.min.css',
    '/css/bootstrap-theme.min.css',
    '/css/fontAwesome.css',
    '/css/hero-slider.css',
    '/css/owl-carousel.css',
    '/css/datepicker.css',
    '/css/templatemo-style.css',
    '/css/maintenance-style.css',
    '/js/vendor/jquery-1.11.2.min.js',
    '/js/vendor/bootstrap.min.js',
    '/js/datepicker.js',
    '/js/plugins.js',
    '/js/main.js',
    '/js/maintenance-app.js',
    '/fonts/fontawesome-webfont.woff2',
    '/fonts/fontawesome-webfont.woff',
    '/fonts/fontawesome-webfont.ttf',
    'https://fonts.googleapis.com/css?family=Cairo:200,300,400,600,700,900&display=swap',
    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'
];

// تثبيت Service Worker
self.addEventListener('install', function(event) {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('Service Worker: Caching files');
                return cache.addAll(urlsToCache);
            })
            .catch(function(error) {
                console.log('Service Worker: Cache failed', error);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', function(event) {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// اعتراض طلبات الشبكة
self.addEventListener('fetch', function(event) {
    console.log('Service Worker: Fetching', event.request.url);
    
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // إرجاع الملف من الكاش إذا وُجد
                if (response) {
                    console.log('Service Worker: Found in cache', event.request.url);
                    return response;
                }
                
                // محاولة جلب الملف من الشبكة
                return fetch(event.request)
                    .then(function(response) {
                        // التحقق من صحة الاستجابة
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // نسخ الاستجابة لحفظها في الكاش
                        const responseToCache = response.clone();
                        
                        caches.open(CACHE_NAME)
                            .then(function(cache) {
                                cache.put(event.request, responseToCache);
                            });
                        
                        return response;
                    })
                    .catch(function(error) {
                        console.log('Service Worker: Fetch failed', error);
                        
                        // إرجاع صفحة بديلة في حالة عدم توفر الإنترنت
                        if (event.request.destination === 'document') {
                            return caches.match('/index.html');
                        }
                    });
            })
    );
});

// معالجة الرسائل من التطبيق الرئيسي
self.addEventListener('message', function(event) {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SAVE_REPORT') {
        // حفظ البلاغ في IndexedDB للمزامنة لاحقاً
        saveReportOffline(event.data.report);
    }
    
    if (event.data && event.data.type === 'SYNC_REPORTS') {
        // مزامنة البلاغات المحفوظة محلياً
        syncOfflineReports();
    }
});

// حفظ البلاغ محلياً عند عدم توفر الإنترنت
function saveReportOffline(report) {
    // فتح قاعدة البيانات المحلية
    const request = indexedDB.open('MaintenanceReports', 1);
    
    request.onerror = function(event) {
        console.error('IndexedDB error:', event.target.error);
    };
    
    request.onsuccess = function(event) {
        const db = event.target.result;
        const transaction = db.transaction(['reports'], 'readwrite');
        const objectStore = transaction.objectStore('reports');
        
        // إضافة معرف فريد ووقت الحفظ
        report.offlineId = Date.now();
        report.savedOffline = true;
        report.syncStatus = 'pending';
        
        const addRequest = objectStore.add(report);
        
        addRequest.onsuccess = function() {
            console.log('Report saved offline successfully');
            
            // إرسال إشعار للمستخدم
            self.registration.showNotification('تم حفظ البلاغ محلياً', {
                body: 'سيتم إرسال البلاغ عند توفر الإنترنت',
                icon: '/img/logo.png',
                badge: '/img/logo.png',
                tag: 'offline-save'
            });
        };
        
        addRequest.onerror = function() {
            console.error('Failed to save report offline');
        };
    };
    
    request.onupgradeneeded = function(event) {
        const db = event.target.result;
        const objectStore = db.createObjectStore('reports', { keyPath: 'offlineId' });
        objectStore.createIndex('syncStatus', 'syncStatus', { unique: false });
    };
}

// مزامنة البلاغات المحفوظة محلياً
function syncOfflineReports() {
    const request = indexedDB.open('MaintenanceReports', 1);
    
    request.onsuccess = function(event) {
        const db = event.target.result;
        const transaction = db.transaction(['reports'], 'readonly');
        const objectStore = transaction.objectStore('reports');
        const index = objectStore.index('syncStatus');
        
        const getRequest = index.getAll('pending');
        
        getRequest.onsuccess = function() {
            const pendingReports = getRequest.result;
            
            if (pendingReports.length > 0) {
                console.log(`Found ${pendingReports.length} reports to sync`);
                
                // محاولة إرسال كل بلاغ
                pendingReports.forEach(function(report) {
                    syncSingleReport(report);
                });
            }
        };
    };
}

// مزامنة بلاغ واحد
function syncSingleReport(report) {
    // محاولة إرسال البلاغ للخادم
    fetch('/api/reports', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(report)
    })
    .then(function(response) {
        if (response.ok) {
            // تحديث حالة المزامنة في قاعدة البيانات المحلية
            updateSyncStatus(report.offlineId, 'synced');
            
            // إرسال إشعار للمستخدم
            self.registration.showNotification('تم إرسال البلاغ', {
                body: 'تم إرسال البلاغ المحفوظ محلياً بنجاح',
                icon: '/img/logo.png',
                badge: '/img/logo.png',
                tag: 'sync-success'
            });
        } else {
            console.error('Failed to sync report:', response.status);
        }
    })
    .catch(function(error) {
        console.error('Sync error:', error);
    });
}

// تحديث حالة المزامنة
function updateSyncStatus(offlineId, status) {
    const request = indexedDB.open('MaintenanceReports', 1);
    
    request.onsuccess = function(event) {
        const db = event.target.result;
        const transaction = db.transaction(['reports'], 'readwrite');
        const objectStore = transaction.objectStore('reports');
        
        const getRequest = objectStore.get(offlineId);
        
        getRequest.onsuccess = function() {
            const report = getRequest.result;
            if (report) {
                report.syncStatus = status;
                report.syncedAt = new Date().toISOString();
                
                const updateRequest = objectStore.put(report);
                updateRequest.onsuccess = function() {
                    console.log('Sync status updated successfully');
                };
            }
        };
    };
}

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', function(event) {
    console.log('Notification clicked:', event.notification.tag);
    
    event.notification.close();
    
    // فتح التطبيق عند النقر على الإشعار
    event.waitUntil(
        clients.openWindow('/')
    );
});

// مزامنة تلقائية في الخلفية
self.addEventListener('sync', function(event) {
    if (event.tag === 'background-sync') {
        console.log('Background sync triggered');
        event.waitUntil(syncOfflineReports());
    }
});

// معالجة تحديث Service Worker
self.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});
